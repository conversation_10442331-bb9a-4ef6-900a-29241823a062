import { z } from 'zod';

const decimalToNumber = z.any().transform((val) => {
  if (val && typeof val === 'object' && 'toNumber' in val) {
    return val.toNumber();
  }
  return Number(val);
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: '<PERSON><PERSON><PERSON>',
  };
  return statusMap[status] || status;
});

const debtorForCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  idNumber: z.string(),
  idType: z.string(),
  email: z.string(),
  phone: z.string(),
  address: z.string(),
  city: z.string().nullable(),
  department: z.string().nullable(),
  birthDate: z.coerce.date().nullable(),
  maritalStatus: z.string().nullable(),
  occupation: z.string().nullable(),
  monthlyIncome: decimalToNumber,
  monthlyExpenses: decimalToNumber,
  dependents: z.coerce.number().nullable(),
  educationLevel: z.string().nullable(),
  totalDebt: decimalToNumber,
  status: z.string(),
  emergencyContact: z.string().nullable(),
  emergencyPhone: z.string().nullable(),
  bankAccount: z.string().nullable(),
  bankName: z.string().nullable(),
  accountType: z.string().nullable(),
  description: z.string().nullable(),
  activeCases: z.coerce.number(),
  createdDate: z.coerce.date(),
  lastUpdate: z.coerce.date(),
});

const operatorForCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
});

const creditorForDebtSchema = z.object({
  name: z.string(),
});

const documentForCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  uploadDate: z.coerce.date(),
  url: z.string(),
  caseId: z.string(),
});

const debtForCaseSchema = z.object({
  id: z.string(),
  amount: decimalToNumber,
  interestRate: decimalToNumber,
  type: z.string(),
  caseId: z.string(),
  creditorId: z.string(),
  debtorId: z.string().nullable(),
  creditor: creditorForDebtSchema,
});

const assetForCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  value: decimalToNumber,
  caseId: z.string(),
  debtorId: z.string().nullable(),
});

export const debtSchema = z.object({
  id: z.string().optional(),
  creditor: z.string().min(1, 'El acreedor es requerido'),
  creditorId: z.string().min(1, 'Debe seleccionar un acreedor'),
  amount: z.string().min(1, 'El monto es requerido'),
  type: z.string().min(1, 'El tipo de deuda es requerido'),
  interestRate: z.string().min(1, 'La tasa de interés es requerida'),
});

export const assetSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'El nombre del bien es requerido'),
  type: z.string().min(1, 'El tipo de bien es requerido'),
  value: z.string().min(1, 'El valor es requerido'),
  description: z.string().optional(),
  location: z.string().optional(),
  registrationNumber: z.string().optional(),
  encumbered: z.boolean().default(false),
  encumbranceDetails: z.string().optional(),
});
export const caseSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
  totalDebt: decimalToNumber,
  creditors: z.coerce.number(),
  createdDate: z.coerce.date(),
  hearingDate: z.coerce.date().nullable(),
  phase: z.string().nullable(),
  causes: z.array(z.string()),
  debtorId: z.string(),
  operatorId: z.string(),

  tramite: z.string().nullable(),
  filingDate: z.coerce.date().nullable(),
  debtorIdNumber: z.string().nullable(),
  convened: z.string().nullable(),
  attorney: z.string().nullable(),
  owedCapital: decimalToNumber.nullable(),
  designatedOperator: z.string().nullable(),
  designationDate: z.coerce.date().nullable(),
  positionAcceptanceDate: z.coerce.date().nullable(),
  inadmissionDate: z.coerce.date().nullable(),
  admissionDate: z.coerce.date().nullable(),
  firstHearingDate: z.coerce.date().nullable(),
  firstHearingTime: z.string().nullable(),
  rejection: z.boolean(),
  withdrawal: z.boolean(),
  hasLegalProcesses: z.boolean(),

  courtNumber: z.string().nullable(),
  city: z.string().nullable(),
  processType: z.string().nullable(),
  plaintiff: z.string().nullable(),
  judicialFileNumber: z.string().nullable(),
  suspensionDate: z.coerce.date().nullable(),
  resultDeliveryDate: z.coerce.date().nullable(),

  resultType: z.string().nullable(),
  resultDate: z.coerce.date().nullable(),
  siccacNumber: z.string().nullable(),
  riskCenterCommunication: z.boolean(),

  debtor: debtorForCaseSchema,
  operator: operatorForCaseSchema,
  _count: z.object({
    documents: z.coerce.number(),
  }),
});

export type Case = z.infer<typeof caseSchema>;

export const caseWithRelationsSchema = caseSchema.extend({
  documents: z.array(documentForCaseSchema),
  debts: z.array(debtForCaseSchema),
  assets: z.array(assetForCaseSchema),
});

export type CaseWithRelations = z.infer<typeof caseWithRelationsSchema>;

export const caseSummarySchema = caseSchema.omit({
  debtor: true,
  operator: true,
  _count: true,
});

export type CaseSummary = z.infer<typeof caseSummarySchema>;
export const createCaseSchema = z.object({
  debtorId: z.string().min(1, 'El deudor es requerido'),
  type: z.enum(['INSOLVENCY', 'CONCILIATION', 'SUPPORT_AGREEMENT'], {
    errorMap: () => ({ message: 'Tipo de caso inválido' }),
  }),
  status: z.string().min(1, 'El estado es requerido'),
  totalDebt: z.union([
    z.number().min(0, 'La deuda total debe ser mayor a 0'),
    z
      .string()
      .min(1, 'La deuda total es requerida')
      .transform((val) => parseFloat(val.replace(/[^0-9.-]/g, ''))),
  ]),
  creditors: z.union([
    z.number().min(1, 'Debe haber al menos un acreedor'),
    z.string().transform((val) => parseInt(val) || 0),
  ]),
  hearingDate: z.string().optional().nullable(),
  phase: z.string().optional(),
  operatorId: z.string().min(1, 'El operador es requerido'),
  caseNumber: z.string().optional(),
  causes: z.array(z.string()).default([]),

  tramite: z.string().optional(),
  filingDate: z.string().optional(),
  debtorIdNumber: z.string().optional(),
  convened: z.string().optional(),
  attorney: z.string().optional(),
  owedCapital: z
    .union([
      z.number().min(0).optional(),
      z
        .string()
        .transform((val) =>
          val ? parseFloat(val.replace(/[^0-9.-]/g, '')) : undefined,
        )
        .optional(),
    ])
    .optional(),
  designatedOperator: z.string().optional(),
  designationDate: z.string().optional(),
  positionAcceptanceDate: z.string().optional(),
  inadmissionDate: z.string().optional(),
  admissionDate: z.string().optional(),
  firstHearingDate: z.string().optional(),
  firstHearingTime: z.string().optional(),
  rejection: z.boolean().default(false),
  withdrawal: z.boolean().default(false),
  hasLegalProcesses: z.boolean().default(false),
  courtNumber: z.string().optional(),
  city: z.string().optional(),
  processType: z.string().optional(),
  plaintiff: z.string().optional(),
  judicialFileNumber: z.string().optional(),
  suspensionDate: z.string().optional(),
  resultDeliveryDate: z.string().optional(),
  resultType: z.string().optional(),
  resultDate: z.string().optional(),
  siccacNumber: z.string().optional(),
  riskCenterCommunication: z.boolean().default(false),
});

export type CreateCaseData = z.infer<typeof createCaseSchema>;
export const updateCaseSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  debtorId: z.string().min(1, 'El deudor es requerido').optional(),
  type: z
    .enum(['INSOLVENCY', 'CONCILIATION', 'SUPPORT_AGREEMENT'], {
      errorMap: () => ({ message: 'Tipo de caso inválido' }),
    })
    .optional(),
  status: z.string().optional(),
  totalDebt: z
    .union([
      z.number().min(0, 'La deuda total debe ser mayor a 0'),
      z
        .string()
        .min(1, 'La deuda total es requerida')
        .transform((val) => parseFloat(val.replace(/[^0-9.-]/g, ''))),
    ])
    .optional(),
  creditors: z
    .union([
      z.number().min(1, 'Debe haber al menos un acreedor'),
      z.string().transform((val) => parseInt(val) || 0),
    ])
    .optional(),
  hearingDate: z.string().optional().nullable(),
  phase: z.string().optional(),
  operatorId: z.string().optional(),
  causes: z.array(z.string()).optional(),

  tramite: z.string().optional(),
  filingDate: z.string().optional(),
  debtorIdNumber: z.string().optional(),
  convened: z.string().optional(),
  attorney: z.string().optional(),
  owedCapital: z
    .union([
      z.number().min(0).optional(),
      z
        .string()
        .transform((val) =>
          val ? parseFloat(val.replace(/[^0-9.-]/g, '')) : undefined,
        )
        .optional(),
    ])
    .optional(),
  designatedOperator: z.string().optional(),
  designationDate: z.string().optional(),
  positionAcceptanceDate: z.string().optional(),
  inadmissionDate: z.string().optional(),
  admissionDate: z.string().optional(),
  firstHearingDate: z.string().optional(),
  firstHearingTime: z.string().optional(),
  rejection: z.boolean().optional(),
  withdrawal: z.boolean().optional(),
  hasLegalProcesses: z.boolean().optional(),
  courtNumber: z.string().optional(),
  city: z.string().optional(),
  processType: z.string().optional(),
  plaintiff: z.string().optional(),
  judicialFileNumber: z.string().optional(),
  suspensionDate: z.string().optional(),
  resultDeliveryDate: z.string().optional(),
  resultType: z.string().optional(),
  resultDate: z.string().optional(),
  siccacNumber: z.string().optional(),
  riskCenterCommunication: z.boolean().optional(),
});

export type UpdateCaseData = z.infer<typeof updateCaseSchema>;
export const addDebtToCaseSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  creditorId: z.string().min(1, 'El ID del acreedor es requerido'),
  amount: z.string().min(1, 'El monto es requerido'),
  type: z.string().min(1, 'El tipo de deuda es requerido'),
  interestRate: z.string().min(1, 'La tasa de interés es requerida'),
});

export type AddDebtToCaseData = z.infer<typeof addDebtToCaseSchema>;
export const debtResponseSchema = z.object({
  id: z.string(),
  amount: decimalToNumber,
  interestRate: decimalToNumber,
  type: z.string(),
  creditor: z.string(),
  creditorId: z.string(),
});
export const removeDebtFromCaseSchema = z.object({
  debtId: z.string().min(1, 'El ID de la deuda es requerido'),
});

export type RemoveDebtFromCaseData = z.infer<typeof removeDebtFromCaseSchema>;
export const caseStatsSchema = z.object({
  total: z.coerce.number(),
  byStatus: z.array(
    z.object({
      status: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
  byType: z.array(
    z.object({
      type: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
  negotiation: z.coerce.number(),
  agreementApproved: z.coerce.number(),
  totalDebt: decimalToNumber,
});

export type CaseStats = z.infer<typeof caseStatsSchema>;
export const creditorOptionSchema = z.object({
  id: z.string(),
  name: z.string(),
  nit: z.string(),
  type: z.string(),
});

export const documentDataSchema = z.object({
  name: z.string(),
  type: z.string(),
  caseId: z.string(),
  debtorName: z.string(),
  size: z.string(),
  format: z.string(),
  additionalInfo: z.string(),
  hearingDate: z.string(),
  hearingTime: z.string(),
  zoomId: z.string(),
  zoomPassword: z.string(),
  paymentTerms: z.string(),
  monthlyAmount: z.string(),
});

export const documentTemplateSchema = z.object({
  id: z.string(),
  fileName: z.string(),
});

export const newDebtorFormSchema = z.object({
  name: z.string(),
  idNumber: z.string(),
  email: z.string(),
  phone: z.string(),
  address: z.string(),
  city: z.string(),
  department: z.string(),
  monthlyIncome: z.number(),
  monthlyExpenses: z.number(),
});
export type DebtData = z.infer<typeof debtSchema>;
export type AssetData = z.infer<typeof assetSchema>;
export type FormDebt = z.infer<typeof debtSchema>;
export type DebtorOption = Pick<
  z.infer<typeof debtorForCaseSchema>,
  | 'id'
  | 'name'
  | 'idNumber'
  | 'email'
  | 'phone'
  | 'address'
  | 'city'
  | 'department'
  | 'monthlyIncome'
  | 'monthlyExpenses'
>;
export type CreditorOption = z.infer<typeof creditorOptionSchema>;
export type NewDebtForm = Pick<
  FormDebt,
  'creditor' | 'amount' | 'type' | 'interestRate'
> & {
  creditorId: string | undefined;
};
export type NewDebtorForm = z.infer<typeof newDebtorFormSchema>;
export type DocumentData = z.infer<typeof documentDataSchema>;
export type DocumentTemplate = z.infer<typeof documentTemplateSchema>;
export type CaseFormSubmissionData = z.infer<typeof createCaseSchema>;
export type EditCaseFormSubmissionData = z.infer<typeof updateCaseSchema>;
export const CaseType = {
  INSOLVENCY: 'INSOLVENCY',
  CONCILIATION: 'CONCILIATION',
  SUPPORT_AGREEMENT: 'SUPPORT_AGREEMENT',
} as const;

export const CaseStatus = {
  NEGOTIATION: 'NEGOTIATION',
  HEARING_SCHEDULED: 'HEARING_SCHEDULED',
  PENDING_DOCUMENTS: 'PENDING_DOCUMENTS',
  AGREEMENT_APPROVED: 'AGREEMENT_APPROVED',
  CLOSED: 'CLOSED',
} as const;

export type CaseTypeEnum = (typeof CaseType)[keyof typeof CaseType];
export type CaseStatusEnum = (typeof CaseStatus)[keyof typeof CaseStatus];
export const getAllCasesSchema = z.array(caseSchema);
export const getCaseByIdSchema = z.string();
export const deleteCaseSchema = z.object({ id: z.string() });
