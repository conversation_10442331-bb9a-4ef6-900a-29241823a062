'use client';

import {
  FileText,
  Search,
  Eye,
  Download,
  Folder,
  FolderOpen,
  ChevronRight,
  ChevronDown,
} from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';

import { type Document } from '@/features/document/schemas';

interface DocumentFileTreeProps {
  documents: Document[];
  onViewDocument: (doc: Document) => void;
  onDownloadDocument: (doc: Document) => void;
}

interface TreeNode {
  id: string;
  name: string;
  type: 'folder' | 'file';
  caseId?: string;
  document?: Document;
  children: TreeNode[];
  isExpanded?: boolean;
}

export function DocumentFileTree({
  documents,
  onViewDocument,
  onDownloadDocument,
}: Readonly<DocumentFileTreeProps>) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(),
  );

  const buildTree = (documents: Document[]): TreeNode[] => {
    const caseGroups = new Map<string, Document[]>();

    documents.forEach((doc) => {
      const docCaseId = doc.caseId || 'sin-caso';
      if (!caseGroups.has(docCaseId)) {
        caseGroups.set(docCaseId, []);
      }
      caseGroups.get(docCaseId)!.push(doc);
    });

    const rootNodes: TreeNode[] = [];

    caseGroups.forEach((caseDocuments, caseId) => {
      const caseNumber = caseDocuments[0]?.case?.caseNumber || caseId;
      const caseFolderNode: TreeNode = {
        id: `case-${caseId}`,
        name: caseNumber,
        type: 'folder',
        caseId,
        children: [],
        isExpanded: expandedFolders.has(caseId),
      };

      // Build template folder structure within each case
      const templateFolderMap = new Map<string, TreeNode>();

      caseDocuments.forEach((doc) => {
        const templatePath = doc.templateFolderPath || [];
        let currentParent = caseFolderNode;

        // Create folder hierarchy based on template folder path
        templatePath.forEach((folderName, index) => {
          const folderKey = `${caseId}-${templatePath.slice(0, index + 1).join('/')}`;

          if (!templateFolderMap.has(folderKey)) {
            const folderNode: TreeNode = {
              id: `template-folder-${folderKey}`,
              name: folderName,
              type: 'folder',
              children: [],
              isExpanded: expandedFolders.has(folderKey),
            };
            templateFolderMap.set(folderKey, folderNode);
            currentParent.children.push(folderNode);
          }

          currentParent = templateFolderMap.get(folderKey)!;
        });

        // Add the document to the appropriate folder
        const fileNode: TreeNode = {
          id: `doc-${doc.id}`,
          name: doc.name,
          type: 'file',
          document: doc,
          children: [],
        };
        currentParent.children.push(fileNode);
      });

      // Sort all children recursively
      const sortChildren = (node: TreeNode) => {
        node.children.sort((a, b) => {
          // Folders first, then files
          if (a.type !== b.type) {
            return a.type === 'folder' ? -1 : 1;
          }
          return a.name.localeCompare(b.name);
        });
        node.children.forEach(sortChildren);
      };

      sortChildren(caseFolderNode);
      rootNodes.push(caseFolderNode);
    });

    rootNodes.sort((a, b) => a.name.localeCompare(b.name));
    return rootNodes;
  };

  const toggleFolder = (folderId: string) => {
    setExpandedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const renderTreeNode = (
    node: TreeNode,
    depth: number = 0,
  ): React.ReactNode => {
    const indentStyle = {
      paddingLeft: `${depth * 24}px`,
    };

    if (node.type === 'folder') {
      const isExpanded = node.isExpanded;
      return (
        <Collapsible
          key={node.id}
          open={isExpanded}
          onOpenChange={() =>
            toggleFolder(node.caseId || node.id.replace('template-folder-', ''))
          }
        >
          <CollapsibleTrigger
            className="flex w-full items-center rounded-md px-3 py-2 text-left hover:bg-gray-100"
            style={indentStyle}
          >
            {isExpanded ? (
              <ChevronDown className="mr-2 h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="mr-2 h-4 w-4 text-gray-500" />
            )}
            {isExpanded ? (
              <FolderOpen className="mr-2 h-5 w-5 text-blue-600" />
            ) : (
              <Folder className="mr-2 h-5 w-5 text-blue-600" />
            )}
            <span className="font-medium text-gray-700">{node.name}</span>
            <span className="ml-auto text-xs text-gray-500">
              {node.children.length} documentos
            </span>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1">
            {node.children.map((child) => renderTreeNode(child, depth + 1))}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    if (node.document) {
      const doc = node.document;
      return (
        <div
          key={node.id}
          className={`flex items-center justify-between rounded-md px-3 py-2 hover:bg-gray-50 ${depth > 0 ? 'bg-gray-50/50' : ''}`}
          style={indentStyle}
        >
          <div className="flex flex-1 items-center">
            <FileText className="mr-2 h-4 w-4 text-gray-500" />
            <span className="truncate text-gray-700">{node.name}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDocument(doc)}
              className="h-8 w-8 p-0"
              title="Ver documento"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDownloadDocument(doc)}
              title="Descargar documento"
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  const treeNodes = buildTree(documents);

  const filteredTreeNodes = treeNodes.filter((node) => {
    if (searchTerm === '') return true;

    const searchLower = searchTerm.toLowerCase();
    const nodeMatches = node.name.toLowerCase().includes(searchLower);
    const childrenMatch = node.children.some((child) =>
      child.name.toLowerCase().includes(searchLower),
    );

    return nodeMatches || childrenMatch;
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Documentos Generados</CardTitle>
            <CardDescription>Documentos organizados por caso</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Buscar documentos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80 pl-10"
              />
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {filteredTreeNodes.length > 0 ? (
            filteredTreeNodes.map((node) => renderTreeNode(node))
          ) : (
            <div className="py-8 text-center text-gray-500">
              <FileText className="mx-auto mb-4 h-12 w-12 text-gray-300" />
              <p>No se encontraron documentos</p>
              <p className="text-sm">
                Genera documentos para un caso para verlos aquí
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
