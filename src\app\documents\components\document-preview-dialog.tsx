'use client';

import { useState, useEffect, useCallback } from 'react';
import { X, Download, Loader2, RefreshCw } from 'lucide-react';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DocumentTemplate } from '@/features/document/types';
import {
  previewDocumentTemplate,
  downloadTemplateFile,
} from '@/features/document/actions';

interface DocumentPreviewDialogProps {
  template: DocumentTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  refreshTrigger?: number;
}

export function DocumentPreviewDialog({
  template,
  open,
  onOpenChange,
  refreshTrigger,
}: Readonly<DocumentPreviewDialogProps>) {
  const [previewHtml, setPreviewHtml] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const { execute: loadPreview, isPending: isLoading } = useServerAction(
    previewDocumentTemplate,
    {
      onSuccess: ({ data }) => {
        setPreviewHtml(data.htmlContent);
        setError(null);
      },
      onError: ({ err }) => {
        setError(err.message || 'Error al cargar la vista previa');
        setPreviewHtml(null);
      },
    },
  );

  const { execute: downloadFile } = useServerAction(downloadTemplateFile, {
    onSuccess: ({ data }) => {
      // Create blob and download
      const blob = new Blob([data.buffer], { type: data.mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = data.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('Archivo descargado exitosamente');
    },
    onError: ({ err }) => {
      toast.error('Error al descargar el archivo: ' + err.message);
    },
  });

  const handleLoadPreview = useCallback(() => {
    if (template) {
      loadPreview({ id: template.id });
    }
  }, [template, loadPreview]);

  useEffect(() => {
    if (open && template) {
      handleLoadPreview();
    } else {
      setPreviewHtml(null);
      setError(null);
    }
  }, [open, template, handleLoadPreview, refreshTrigger]);

  const handleDownload = () => {
    if (template) {
      downloadFile({ id: template.id });
    }
  };

  const handleClose = () => {
    onOpenChange(false);
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent
        className="max-h-[90vh] w-[90vw] overflow-hidden sm:max-w-6xl"
        showCloseButton={false}
      >
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">
              Vista Previa: {template.fileName}
            </DialogTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleLoadPreview}
                disabled={isLoading}
                title="Actualizar vista previa"
              >
                <RefreshCw
                  className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
                Actualizar
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                disabled={isLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Descargar
              </Button>
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-col gap-4 overflow-hidden">
          {isLoading && (
            <div className="flex h-96 items-center justify-center">
              <div className="text-center">
                <Loader2 className="mx-auto h-8 w-8 animate-spin text-blue-600" />
                <p className="mt-2 text-sm text-gray-600">
                  Cargando vista previa...
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="flex h-96 items-center justify-center">
              <div className="text-center">
                <p className="font-medium text-red-600">Error</p>
                <p className="mt-1 text-sm text-gray-600">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLoadPreview}
                  className="mt-4"
                >
                  Reintentar
                </Button>
              </div>
            </div>
          )}

          {previewHtml && !isLoading && !error && (
            <>
              <div className="max-h-[60vh] flex-1 overflow-y-auto rounded-lg border bg-white">
                <div
                  className="p-8 font-sans leading-relaxed text-gray-800"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              </div>
              <div className="rounded-lg bg-gray-50 p-4">
                <h4 className="mb-2 font-medium text-gray-900">
                  Información del Documento
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Archivo:</span>
                    <span className="ml-2 text-gray-600">
                      {template.fileName}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Tipo:</span>
                    <span className="ml-2 text-gray-600">
                      {template.mimeType.includes('word')
                        ? 'Word'
                        : 'Documento'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">
                      Placeholders:
                    </span>
                    <span className="ml-2 text-gray-600">
                      {template.placeholders?.length || 0} campos
                    </span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
