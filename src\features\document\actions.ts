'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import Docxtemplater from 'docxtemplater';
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip';
import libre from 'libreoffice-convert';
import { promisify } from 'util';
import mammoth from 'mammoth';

import prisma from '@/lib/prisma';

const libreConvert = promisify(libre.convert);

import {
  documentFilterSchema,
  getDocumentsSchema,
  downloadDocumentTemplateInputSchema,
  downloadDocumentTemplateOutputSchema,
  getTemplatesFromDatabaseInputSchema,
  getTemplatesFromDatabaseOutputSchema,
  syncTemplatesWithGoogleDriveInputSchema,
  syncTemplatesWithGoogleDriveOutputSchema,
  batchGenerateDocumentsInputSchema,
  batchGenerateDocumentsOutputSchema,
  previewDocumentTemplateInputSchema,
  previewDocumentTemplateOutputSchema,
  downloadTemplateFileInputSchema,
  downloadTemplateFileOutputSchema,
  caseWithRelationsSchema,
  type CaseWithRelations,
} from './schemas';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

export const downloadDocumentTemplate = createServerAction()
  .input(downloadDocumentTemplateInputSchema)
  .output(downloadDocumentTemplateOutputSchema)
  .handler(async ({ input: { id } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(
      template.googleDriveId,
    );

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    return {
      buffer,
      fileName: fileInfo.name || template.fileName || 'template.docx',
      mimeType:
        fileInfo.mimeType || template.mimeType || 'application/octet-stream',
    };
  });

export const getTemplatesFromDatabase = createServerAction()
  .input(getTemplatesFromDatabaseInputSchema)
  .output(getTemplatesFromDatabaseOutputSchema)
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: [{ isFolder: 'desc' }, { fileName: 'asc' }],
    });

    return templates.map((template) => ({
      id: template.id,
      googleDriveId: template.googleDriveId,
      fileName: template.fileName,
      mimeType: template.mimeType,
      placeholders: template.placeholders as Array<{
        key: string;
        label: string;
      }>,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
      folderPath: template.folderPath,
      isFolder: template.isFolder,
      parentFolderId: template.parentFolderId || undefined,
    }));
  });

export const syncTemplatesWithGoogleDrive = createServerAction()
  .input(syncTemplatesWithGoogleDriveInputSchema)
  .output(syncTemplatesWithGoogleDriveOutputSchema)
  .handler(async () => {
    console.log('Starting Google Drive sync process...');
    const { googleDriveService } = await import('@/lib/google-drive');

    console.log('Fetching folders and files from Google Drive...');
    const driveData =
      await googleDriveService.listAllFoldersAndFilesRecursively();
    console.log(
      `Found ${driveData.folders.length} folders and ${driveData.files.length} files in Google Drive`,
    );

    let created = 0;
    let updated = 0;
    let folders = 0;

    for (const folder of driveData.folders) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: folder.id },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: folder.name,
            folderPath: folder.path,
            parentFolderId: folder.parentId,
            isFolder: true,
            updatedAt: folder.modifiedTime
              ? new Date(folder.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: folder.id,
            fileName: folder.name,
            mimeType: 'application/vnd.google-apps.folder',
            placeholders: [],
            folderPath: folder.path,
            parentFolderId: folder.parentId,
            isFolder: true,
            createdAt: folder.createdTime
              ? new Date(folder.createdTime)
              : new Date(),
            updatedAt: folder.modifiedTime
              ? new Date(folder.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
      folders++;
    }

    const templateFiles = driveData.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc') ||
        file.mimeType?.includes('spreadsheet') ||
        file.name?.toLowerCase().endsWith('.xlsx') ||
        file.name?.toLowerCase().endsWith('.xls'),
    );

    for (const file of templateFiles) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: file.id },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: file.name,
            mimeType: file.mimeType || 'application/octet-stream',
            folderPath: file.path,
            parentFolderId: file.parentId,
            isFolder: false,
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id,
            fileName: file.name,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            folderPath: file.path,
            parentFolderId: file.parentId,
            isFolder: false,
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
    }

    const syncResult = {
      synced: templateFiles.length + folders,
      created,
      updated,
      folders,
    };

    console.log('Sync completed:', syncResult);
    return syncResult;
  });

export const batchGenerateDocuments = createServerAction()
  .input(batchGenerateDocumentsInputSchema)
  .output(batchGenerateDocumentsOutputSchema)
  .handler(async ({ input: { caseId, templateIds, outputFormat } }) => {
    try {
      const caseData = await getCaseWithRelationsForGeneration(caseId);
      if (!caseData) {
        throw new Error('Caso no encontrado');
      }

      const templates = await getTemplatesToProcess(templateIds);
      if (templates.length === 0) {
        throw new Error('No se encontraron plantillas para procesar');
      }

      const caseFolder = await createCaseFolderInGoogleDrive(
        caseData.caseNumber,
      );

      const validatedCaseData = caseWithRelationsSchema.parse({
        ...caseData,
        debts:
          caseData.debts?.map((debt) => ({
            ...debt,
            amount: Number(debt.amount),
            creditor: debt.creditor
              ? {
                  ...debt.creditor,
                }
              : null,
          })) || [],
        assets:
          caseData.assets?.map((asset) => ({
            ...asset,
            estimatedValue: asset.value ? Number(asset.value) : null,
          })) || [],
        debtor: caseData.debtor
          ? {
              ...caseData.debtor,
              monthlyIncome: caseData.debtor.monthlyIncome
                ? Number(caseData.debtor.monthlyIncome)
                : null,
            }
          : null,
      });

      const results = await Promise.allSettled(
        templates.map((template) =>
          processTemplateForCase(
            template,
            validatedCaseData,
            caseFolder,
            outputFormat,
          ),
        ),
      );

      const documents = results.map((result, index) => {
        const template = templates[index];
        if (result.status === 'fulfilled') {
          return {
            templateId: template.id,
            templateName: template.fileName,
            documentId: result.value.documentId,
            documentName: result.value.documentName,
            documentUrl: result.value.documentUrl,
            status: 'success' as const,
          };
        } else {
          return {
            templateId: template.id,
            templateName: template.fileName,
            documentId: '',
            documentName: '',
            documentUrl: '',
            status: 'error' as const,
            error: result.reason?.message || 'Error desconocido',
          };
        }
      });

      const successfulDocuments = documents.filter(
        (doc) => doc.status === 'success',
      ).length;
      const failedDocuments = documents.length - successfulDocuments;

      revalidatePath('/documents');

      return {
        success: true,
        message: `Generación completada: ${successfulDocuments} exitosos, ${failedDocuments} fallidos`,
        data: {
          caseId: caseData.id,
          caseNumber: caseData.caseNumber,
          folderId: caseFolder.id,
          folderUrl: caseFolder.webViewLink,
          documents,
          generatedAt: new Date(),
          totalDocuments: documents.length,
          successfulDocuments,
          failedDocuments,
        },
      };
    } catch (error) {
      console.error('Error in batch document generation:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Error en la generación por lotes',
        data: null,
      };
    }
  });

async function getCaseWithRelationsForGeneration(caseId: string) {
  return await prisma.case.findUnique({
    where: { id: caseId },
    include: {
      debtor: true,
      operator: true,
      debts: {
        include: {
          creditor: true,
        },
      },
      assets: true,
      documents: true,
    },
  });
}

async function getTemplatesToProcess(templateIds?: string[]) {
  if (templateIds && templateIds.length > 0) {
    return await prisma.documentTemplate.findMany({
      where: {
        id: { in: templateIds },
      },
    });
  } else {
    return await prisma.documentTemplate.findMany();
  }
}

async function createCaseFolderInGoogleDrive(caseNumber: string) {
  const { googleDriveService } = await import('@/lib/google-drive');
  const casesFolderId = '1jV6Kf5Y7jDPDUn2vmzpSvFGosftsWS_2';

  const caseFolderId = await googleDriveService.findOrCreateCaseFolder(
    caseNumber,
    casesFolderId,
  );

  return {
    id: caseFolderId,
    name: caseNumber,
    webViewLink: googleDriveService.getFolderUrl(caseFolderId),
  };
}

async function processTemplateForCase(
  template: {
    id: string;
    googleDriveId: string;
    fileName: string;
  },
  caseData: CaseWithRelations,
  caseFolder: { id: string; name: string; webViewLink: string },
  outputFormat: 'docx' | 'pdf',
) {
  try {
    const { googleDriveService } = await import('@/lib/google-drive');

    const templateBuffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    const generatedBuffer = await generateDocumentWithPlaceholdersInternal(
      templateBuffer,
      caseData,
      outputFormat,
    );

    const documentName = `${template.fileName.replace(/\.[^/.]+$/, '')}_${caseData.caseNumber}.${outputFormat}`;

    const { fileId, fileUrl } = await googleDriveService.uploadDocumentToCase(
      documentName,
      generatedBuffer,
      caseFolder.id,
    );

    const document = await prisma.document.create({
      data: {
        name: documentName,
        type: getDocumentTypeFromTemplate(template.fileName),
        status: 'Generado',
        uploadDate: new Date(),
        url: fileUrl,
        googleDriveId: fileId,
        caseId: caseData.id,
        templateId: template.id,
      },
    });

    return {
      documentId: document.id,
      documentName,
      documentUrl: fileUrl,
    };
  } catch (error) {
    console.error(`Error processing template ${template.fileName}:`, error);
    throw error;
  }
}

async function generateDocumentWithPlaceholdersInternal(
  templateBuffer: Buffer,
  caseData: CaseWithRelations,
  outputFormat: 'docx' | 'pdf',
): Promise<Buffer> {
  try {
    const zip = new PizZip(templateBuffer);
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true,
    });

    const placeholderData = createPlaceholderData(caseData);

    doc.render(placeholderData);

    const generatedBuffer = doc.getZip().generate({ type: 'nodebuffer' });

    if (outputFormat === 'pdf') {
      return await libreConvert(generatedBuffer, '.pdf', undefined);
    }

    return generatedBuffer;
  } catch (error) {
    console.error('Error generating document with placeholders:', error);
    throw new Error('Error al generar documento con placeholders');
  }
}

function createPlaceholderData(caseData: CaseWithRelations) {
  return {
    case: {
      id: caseData.id,
      caseNumber: caseData.caseNumber,
      type: caseData.type,
      status: caseData.status,
      createdAt: caseData.createdAt,
      updatedAt: caseData.updatedAt,
    },

    debtor: caseData.debtor
      ? {
          id: caseData.debtor.id,
          name: caseData.debtor.name,
          documentType: caseData.debtor.documentType,
          documentNumber: caseData.debtor.documentNumber,
          email: caseData.debtor.email,
          phone: caseData.debtor.phone,
          address: caseData.debtor.address,
          city: caseData.debtor.city,
          department: caseData.debtor.department,
          country: caseData.debtor.country,
          birthDate: caseData.debtor.birthDate,
          maritalStatus: caseData.debtor.maritalStatus,
          occupation: caseData.debtor.occupation,
          monthlyIncome: caseData.debtor.monthlyIncome,
          createdAt: caseData.debtor.createdAt,
          updatedAt: caseData.debtor.updatedAt,
        }
      : null,

    operator: caseData.operator
      ? {
          id: caseData.operator.id,
          name: caseData.operator.name,
          email: caseData.operator.email,
          role: caseData.operator.role,
        }
      : null,

    debts: caseData.debts
      ? caseData.debts.map((debt) => ({
          id: debt.id,
          amount: debt.amount,
          currency: debt.currency,
          description: debt.description,
          dueDate: debt.dueDate,
          status: debt.status,
          creditor: debt.creditor
            ? {
                id: debt.creditor.id,
                name: debt.creditor.name,
                documentType: debt.creditor.documentType,
                documentNumber: debt.creditor.documentNumber,
                email: debt.creditor.email,
                phone: debt.creditor.phone,
                address: debt.creditor.address,
              }
            : null,
        }))
      : [],

    assets: caseData.assets
      ? caseData.assets.map((asset) => ({
          id: asset.id,
          type: asset.type,
          description: asset.description || null,
          estimatedValue: asset.estimatedValue || null,
          currency: asset.currency || null,
          location: asset.location || null,
          status: asset.status,
        }))
      : [],

    currentDate: new Date().toLocaleDateString('es-CO'),
    currentDateTime: new Date().toLocaleString('es-CO'),

    totalDebts: caseData.debts
      ? caseData.debts.reduce(
          (sum: number, debt) => sum + (Number(debt.amount) || 0),
          0,
        )
      : 0,
    totalAssets: caseData.assets
      ? caseData.assets.reduce(
          (sum: number, asset) => sum + (Number(asset.estimatedValue) || 0),
          0,
        )
      : 0,
    debtCount: caseData.debts ? caseData.debts.length : 0,
    assetCount: caseData.assets ? caseData.assets.length : 0,
  };
}

function getDocumentTypeFromTemplate(fileName: string): string {
  const lowerFileName = fileName.toLowerCase();

  if (lowerFileName.includes('solicitud')) return 'Solicitud';
  if (lowerFileName.includes('declaracion')) return 'Declaración';
  if (lowerFileName.includes('certificado')) return 'Certificado';
  if (lowerFileName.includes('check') || lowerFileName.includes('lista'))
    return 'Lista de Verificación';
  if (lowerFileName.includes('designacion')) return 'Designación';
  if (lowerFileName.includes('auto')) return 'Auto';
  if (lowerFileName.includes('inadmision')) return 'Inadmisión';
  if (lowerFileName.includes('notificacion')) return 'Notificación';
  if (lowerFileName.includes('edicto')) return 'Edicto';
  if (lowerFileName.includes('suspension')) return 'Suspensión';
  if (lowerFileName.includes('formato')) return 'Formato';
  if (lowerFileName.includes('remision')) return 'Remisión';
  if (lowerFileName.includes('guia')) return 'Guía';
  if (lowerFileName.includes('hoja')) return 'Hoja de Trabajo';

  return 'Documento';
}

export const previewDocumentTemplate = createServerAction()
  .input(previewDocumentTemplateInputSchema)
  .output(previewDocumentTemplateOutputSchema)
  .handler(async ({ input: { id } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(
      template.googleDriveId,
    );

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    const fileName = fileInfo.name || template.fileName || 'template.docx';
    const mimeType =
      fileInfo.mimeType || template.mimeType || 'application/octet-stream';

    const isWordDocument =
      mimeType ===
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      mimeType === 'application/msword' ||
      fileName.toLowerCase().endsWith('.docx') ||
      fileName.toLowerCase().endsWith('.doc');

    let htmlContent = '';

    if (isWordDocument) {
      const htmlResult = await mammoth.convertToHtml({ buffer });
      htmlContent = htmlResult.value;
    } else {
      const fileExtension =
        fileName.split('.').pop()?.toUpperCase() || 'DESCONOCIDO';
      htmlContent = `
        <div style="text-align: center; padding: 40px; background-color: #f8f9fa; border-radius: 8px; border: 2px dashed #dee2e6;">
          <div style="font-size: 48px; margin-bottom: 20px;">📄</div>
          <h2 style="color: #495057; margin-bottom: 10px;">Vista previa no disponible</h2>
          <p style="color: #6c757d; margin-bottom: 20px;">
            Este archivo es de tipo <strong>${fileExtension}</strong> y no se puede mostrar como vista previa.
          </p>
          <p style="color: #6c757d; font-size: 14px;">
            Puedes descargar el archivo para ver su contenido completo.
          </p>
          <div style="margin-top: 30px; padding: 20px; background-color: white; border-radius: 6px; border-left: 4px solid #007bff;">
            <h4 style="margin: 0 0 10px 0; color: #007bff;">Información del archivo:</h4>
            <p style="margin: 5px 0; color: #495057;"><strong>Nombre:</strong> ${fileName}</p>
            <p style="margin: 5px 0; color: #495057;"><strong>Tipo:</strong> ${mimeType}</p>
            <p style="margin: 5px 0; color: #495057;"><strong>Formato:</strong> ${fileExtension}</p>
          </div>
        </div>
      `;
    }

    return {
      htmlContent,
      fileName,
    };
  });

export const downloadTemplateFile = createServerAction()
  .input(downloadTemplateFileInputSchema)
  .output(downloadTemplateFileOutputSchema)
  .handler(async ({ input: { id } }) => {
    const [result] = await downloadDocumentTemplate({ id });

    if (!result) {
      throw new Error('Error al descargar la plantilla');
    }

    return result;
  });
