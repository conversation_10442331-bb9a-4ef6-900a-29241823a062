'use client';

import { useState } from 'react';

import { DashboardHeader } from '../dashboard/components/dashboard-header';
import { DocumentsHeader } from './components/documents-header';
import { DocumentGeneration } from './components/document-generation';
import { PlaceholdersDialog } from './components/placeholders-dialog';
import { DocumentTabs } from './components/document-tabs';

export default function DocumentsPage() {
  const [showPlaceholdersDialog, setShowPlaceholdersDialog] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleTemplatesSynced = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleDocumentsGenerated = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <DocumentsHeader
            onShowPlaceholders={() => setShowPlaceholdersDialog(true)}
            onTemplatesSynced={handleTemplatesSynced}
          />

          <DocumentGeneration onDocumentsGenerated={handleDocumentsGenerated} />

          <DocumentTabs refreshTrigger={refreshTrigger} />

          <PlaceholdersDialog
            open={showPlaceholdersDialog}
            onOpenChange={setShowPlaceholdersDialog}
          />
        </div>
      </main>
    </div>
  );
}
