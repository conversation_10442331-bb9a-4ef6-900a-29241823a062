import { PrismaClient } from '@prisma/client';

import { main as seedMain } from './seed';

const prisma = new PrismaClient();

async function main() {
  await prisma.document.deleteMany({});
  await prisma.documentTemplate.deleteMany({});
  await prisma.legalProcess.deleteMany({});
  await prisma.asset.deleteMany({});
  await prisma.debt.deleteMany({});
  await prisma.activityLog.deleteMany({});

  await prisma.case.deleteMany({});

  await prisma.contact.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.creditor.deleteMany({});
  await prisma.debtor.deleteMany({});

  await prisma.role.deleteMany({});
}

main()
  .then(async () => {
    await seedMain();
    await prisma.$disconnect();
  })
  .catch(async () => {
    await prisma.$disconnect();
    process.exit(1);
  });
