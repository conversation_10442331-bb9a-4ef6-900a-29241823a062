import { z } from 'zod';

const mapDocumentTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    CEDULA: 'Cédula',
    RUT: 'RUT',
    ESTADOS_FINANCIEROS: 'Estados Financieros',
    CERTIFICADO_INGRESOS: 'Certificado de Ingresos',
    ESCRITURA_PUBLICA: 'Escritura Pública',
    AUTORIZACION: 'Autorización',
    DEMANDA: 'Demanda',
    RESPUESTA_DEMANDA: 'Respuesta a Demanda',
    ACUERDO: 'Acuerdo',
    SENTENCIA: 'Sentencia',
    OTRO: 'Otro',
  };
  return typeMap[type] || type;
});

const mapDocumentStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    PENDIENTE: 'Pendiente',
    APROBADO: 'Aprobado',
    RECHAZADO: 'Rechazado',
    EN_REVISION: 'En Revisión',
  };
  return statusMap[status] || status;
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
});

const caseForDocumentSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
});

export const documentWithCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: mapDocumentTypeToSpanish,
  status: mapDocumentStatusToSpanish,
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
  case: caseForDocumentSchema,
});

export type DocumentWithCase = z.infer<typeof documentWithCaseSchema>;

export const documentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string().optional(),
  uploadDate: z.coerce.date().optional(),
  caseId: z.string().optional(),
  case: caseForDocumentSchema.optional(),
  debtorName: z.string().optional(),
  createdDate: z.string().optional(),
  size: z.string().optional(),
  format: z.string().optional(),
  createdBy: z.string().optional(),
  downloadCount: z.coerce.number().optional(),
  lastAccessed: z.string().optional(),
  viewCount: z.coerce.number().optional(),
  shareCount: z.coerce.number().optional(),
});

export type Document = z.infer<typeof documentSchema>;

export const documentFilterSchema = z.object({
  caseId: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
});

export type DocumentFilter = z.infer<typeof documentFilterSchema>;

// Esquemas para plantillas de documentos
export const documentTemplateSchema = z.object({
  id: z.string(),
  googleDriveId: z.string(),
  fileName: z.string(),
  mimeType: z.string(),
  placeholders: z.array(
    z.object({
      key: z.string(),
      label: z.string(),
    }),
  ),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
  // Folder hierarchy support
  folderPath: z.array(z.string()).default([]),
  parentFolderId: z.string().optional(),
  isFolder: z.boolean().default(false),
});

export type DocumentTemplate = z.infer<typeof documentTemplateSchema>;

// Case with relations schema for batch document generation
export const debtorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    documentType: z.string().optional(),
    documentNumber: z.string().optional(),
    email: z.string().optional().nullable(),
    phone: z.string().optional().nullable(),
    address: z.string().optional().nullable(),
    city: z.string().optional().nullable(),
    department: z.string().optional().nullable(),
    country: z.string().optional().nullable(),
    birthDate: z.coerce.date().optional().nullable(),
    maritalStatus: z.string().optional().nullable(),
    occupation: z.string().optional().nullable(),
    monthlyIncome: z.number().optional().nullable(),
    createdAt: z.coerce.date().optional(),
    updatedAt: z.coerce.date().optional(),
    createdDate: z.coerce.date().optional(),
    lastUpdate: z.coerce.date().optional(),
  })
  .catchall(z.unknown());

export const operatorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    role: z.string().optional(),
  })
  .catchall(z.unknown());

export const creditorSchema = z
  .object({
    id: z.string(),
    name: z.string(),
    documentType: z.string().optional(),
    documentNumber: z.string().optional(),
    email: z.string().optional().nullable(),
    phone: z.string().optional().nullable(),
    address: z.string().optional().nullable(),
  })
  .catchall(z.unknown());

export const debtSchema = z
  .object({
    id: z.string(),
    amount: z.number().transform((val) => Number(val)), // Handle Decimal conversion
    currency: z.string(),
    description: z.string().optional().nullable(),
    dueDate: z.coerce.date().optional().nullable(),
    status: z.string(),
    creditor: creditorSchema.optional().nullable(),
  })
  .catchall(z.unknown());

export const assetSchema = z
  .object({
    id: z.string(),
    type: z.string(),
    description: z.string().optional().nullable(),
    estimatedValue: z.number().optional().nullable(),
    currency: z.string().optional().nullable(),
    location: z.string().optional().nullable(),
    status: z.string(),
  })
  .catchall(z.unknown());

export const caseWithRelationsSchema = z
  .object({
    id: z.string(),
    caseNumber: z.string(),
    type: z.string().optional(),
    status: z.string().optional(),
    createdAt: z.coerce.date().optional(),
    updatedAt: z.coerce.date().optional(),
    createdDate: z.coerce.date().optional(),
    lastUpdate: z.coerce.date().optional(),
    debtor: debtorSchema.optional().nullable(),
    operator: operatorSchema.optional().nullable(),
    debts: z.array(debtSchema).optional(),
    assets: z.array(assetSchema).optional(),
  })
  .catchall(z.unknown());

export type CaseWithRelations = z.infer<typeof caseWithRelationsSchema>;
export type Debtor = z.infer<typeof debtorSchema>;
export type Operator = z.infer<typeof operatorSchema>;
export type Debt = z.infer<typeof debtSchema>;
export type Asset = z.infer<typeof assetSchema>;
export type Creditor = z.infer<typeof creditorSchema>;

export const getDocumentsSchema = z.array(documentWithCaseSchema);
